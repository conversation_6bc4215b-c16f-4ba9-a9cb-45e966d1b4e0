#![allow(unused)]
#![allow(elided_lifetimes_in_paths)]
#![allow(invalid_reference_casting)]
mod anki;
mod edgetts_rs;
mod license;
pub mod llm;
mod signals;
mod wbi_sign;
use crate::signals::*;
use rinf::{DartSignal, RustSignal};
// 导出llm模块中的函数供外部使用
pub use crate::llm::index::generate_cards;
pub use crate::llm::index::parse_card_response;
// Uncomment below to target the web.
// use tokio_with_wasm::alias as tokio;
rinf::write_interface!();
use anki::docx_card;
use anki::mind_card::markdown::{make_markdown_card, MarkdownCardConfig};
use anki::mind_card::mubu::{make_mubu_card, MubuCardConfig};
use anki::mind_card::xmind::{make_xmind_card, XmindCardConfig};
use anki::mind_card::zhixi::{make_zhixi_card, ZhixiCardConfig};
use anki::models::{gen_apkg, AnkiNote};
use anki::pdf_card::cloze_card::{make_occlusion_card, OcclusionCardConfig};
use anki::pdf_card::qa_card::{
    make_qa_card_dual_file, make_qa_card_full_page, make_qa_card_single_file, QACardConfig,
};
use anki::pdf_card::zotero::{make_zotero_annotation_cards, ZoteroCardConfig};
use anki::sync;
use anki::text_card::make_judge_card;


use edgetts_rs::tts::gen_tts;
use futures::FutureExt;
use rinf::debug_print;
use std::collections::HashMap;

/// Helper struct to handle response messages
#[derive(Clone, Debug)]
struct ResponseHandler {
    interaction_id: String,
}

impl ResponseHandler {
    fn new(interaction_id: String) -> Self {
        Self {
            interaction_id,
        }
    }

    fn send_response(&self, status: &str, message: &str, data: String) {
        RustResponse {
            interaction_id: self.interaction_id.clone(),
            status: status.to_string(),
            message: message.to_string(),
            data,
        }
        .send_signal_to_dart();
    }
}

/// 创建一个进度回调函数
fn progress_callback(current: f64, total: f64, message: String) {
    ProgressResponse {
        status: "running".to_string(),
        message,
        current,
        total,
    }
    .send_signal_to_dart();
}

/// 创建一个空的进度回调函数（不执行任何操作）
fn no_op_progress_callback(_current: f64, _total: f64, _message: String) {
    // 不执行任何操作，用于禁用进度报告时
}

/// 根据show_progress参数创建条件进度回调函数
fn create_conditional_progress_callback(
    show_progress: bool,
) -> impl Fn(f64, f64, String) + Clone + Send + Sync {
    if show_progress {
        progress_callback
    } else {
        no_op_progress_callback
    }
}

/// 根据show_progress参数创建条件进度回调函数
fn create_conditional_progress_callback_with_handler(
    show_progress: bool,
    _handler: ResponseHandler,
) -> impl Fn(f64, f64, String) + Clone + Send + Sync {
    move |current: f64, total: f64, message: String| {
        if show_progress {
            ProgressResponse {
                status: "running".to_string(),
                message,
                current,
                total,
            }
            .send_signal_to_dart();
        }
    }
}

pub async fn communicate() {
    let receiver = DartRequest::get_dart_signal_receiver(); // GENERATED
    while let Some(dart_signal) = receiver.recv().await {
        let message = dart_signal.message;
        let data = message.params;
        let msg_type = message.msg_type;
        let interaction_id = message.interaction_id;

        debug_print!("{interaction_id}");
        debug_print!("{data:?}");
        let data_str = data.as_str();
        let params: serde_json::Value = match serde_json::from_str(data_str) {
            Ok(json) => json,
            Err(e) => {
                debug_print!("JSON parsing error: {}", e);
                RustResponse {
                    interaction_id: interaction_id.to_string(),
                    status: "error".to_string(),
                    message: format!("JSON parsing error: {e}").to_string(),
                    data: "".to_string(),
                }
                .send_signal_to_dart();
                return;
            }
        };
        debug_print!("Parsed JSON: {:?}", params);
        let mut show_progress = params
            .get("show_progress")
            .and_then(|v| v.as_bool())
            .unwrap_or(true);
        let no_progress_types = vec!["verify_license", "open_file", "wbi_sign"];
        if no_progress_types.contains(&msg_type.as_str()) {
            show_progress = false;
        }
        if show_progress {
            ProgressResponse {
                status: "running".to_string(),
                message: "处理中...".to_string(),
                current: 1.,
                total: 100.,
            }
            .send_signal_to_dart();
        }
        match Some(msg_type.as_str()) {
            // 验证许可证
            Some("verify_license") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let path = params["path"].as_str().unwrap_or("");
                let machineid = params["machineid"].as_str().unwrap_or("");
                match license::verify_license_local(path, machineid) {
                    Ok(result) => {
                        debug_print!("License verification result: {}", result);
                        if result {
                            // License is valid
                            handler.send_response("success", "License verification passed", "true".to_string());
                        } else {
                            // License is invalid (expired, wrong machine ID, invalid signature, etc.)
                            handler.send_response("error", "License verification failed", "false".to_string());
                        }
                    }
                    Err(e) => {
                        debug_print!("License verification error: {}", e);
                        handler.send_response("error", &e.to_string(), "false".to_string());
                    }
                }
            }
            // 获取许可证路径
            Some("get_license_path") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                match license::get_license_path() {
                    Ok(path) => {
                        handler.send_response("success", "", path);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // 激活请求
            Some("request_activate") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let url = params["url"].as_str().unwrap_or("").to_string();
                let code = params["code"].as_str().unwrap_or("").to_string();
                let machineid = params["machineid"].as_str().filter(|s| !s.is_empty()).map(|s| s.to_string());

                let handler_clone = handler.clone();
                tokio::spawn(async move {
                    match license::request_activate(&url, &code, machineid.as_deref()).await {
                        Ok(response) => {
                            let response_json = serde_json::json!({
                                "status": response.status,
                                "message": response.message,
                                "data": response.data
                            });
                            handler_clone.send_response("success", "", response_json.to_string());
                        }
                        Err(e) => {
                            handler_clone.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // 注销请求
            Some("request_unregister") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let url = params["url"].as_str().unwrap_or("").to_string();
                let machineid = params["machineid"].as_str().filter(|s| !s.is_empty()).map(|s| s.to_string());

                let handler_clone = handler.clone();
                tokio::spawn(async move {
                    match license::request_unregister(&url, machineid.as_deref()).await {
                        Ok(response) => {
                            let response_json = serde_json::json!({
                                "status": response.status,
                                "message": response.message,
                                "data": response.data
                            });
                            handler_clone.send_response("success", "", response_json.to_string());
                        }
                        Err(e) => {
                            handler_clone.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // WBI签名
            Some("wbi_sign") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let params_str = params["params"].as_str().unwrap_or_default();
                let img_key = params["img_key"].as_str().unwrap_or_default().to_string();
                let sub_key = params["sub_key"].as_str().unwrap_or_default().to_string();
                match wbi_sign::encode_wbi(params_str, (img_key, sub_key)) {
                    Ok(result) => {
                        handler.send_response("success", "", result);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // 打开文件
            Some("open_file") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let _ = open::that(&path).unwrap();
                handler.send_response("success", "", "".to_string());
            }
            // 读取文件内容(自动检测编码)
            Some("read_file_auto_decode") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let content = anki::utils::read_file_auto_decode(&path).unwrap_or_default();
                handler.send_response("success", "", content);
            }
            // 生成apkg
            Some("anki/gen_apkg") => {
                // Parse notes array
                let notes: Vec<AnkiNote> = params["notes"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .map(|note| serde_json::from_value(note.clone()).unwrap_or_default())
                    .collect();

                // Parse media list
                let media_list = params["media_list"].as_array().map(|arr| {
                    arr.iter()
                        .map(|s| s.as_str().unwrap_or_default().to_string())
                        .collect()
                });

                // Get output path
                let output_path = params["output_path"].as_str().unwrap_or_default();
                // Get address
                let address = params["address"]
                    .as_str()
                    .filter(|s| !s.is_empty())
                    .map(|s| s);
                let remove_media = params["remove_media"].as_bool().unwrap_or(false);
                let internal_media_types = params["internal_media_types"].as_array().map(|arr| {
                    arr.iter()
                        .map(|s| s.as_str().unwrap_or_default().to_string())
                        .collect()
                });
                // Generate package
                let handler = ResponseHandler::new(interaction_id.to_string());
                match gen_apkg(
                    notes,
                    media_list,
                    output_path,
                    address,
                    remove_media,
                    internal_media_types,
                )
                .await
                {
                    Ok(result) => {
                        handler.send_response("success", "", result);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // OCR识别
            Some("anki/ocr") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                // 解析参数
                let file_paths: Vec<String> = params["file_paths"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let provider = params["provider"].as_str().unwrap_or("");
                let model_name = params["model_name"].as_str().unwrap_or("");
                let api_key = params["api_key"].as_str().unwrap_or("");
                let base_url = params["base_url"].as_str().unwrap_or("");
                let system_prompt = params["system_prompt"].as_str().unwrap_or("");
                let protocol_type = params["protocol_type"].as_str().unwrap_or("");
                let merge_output = params["merge_output"].as_bool().unwrap_or(false);
                let response_format = params["response_format"].as_str().unwrap_or("txt");
                // 调用OCR
                // 创建条件进度回调函数
                let progress_cb = create_conditional_progress_callback_with_handler(show_progress, handler.clone());

                match crate::anki::ocr::image_ocr(
                    &file_paths,
                    provider,
                    model_name,
                    api_key,
                    base_url,
                    system_prompt,
                    protocol_type,
                    merge_output,
                    response_format,
                    progress_cb,
                )
                .await
                {
                    Ok(result) => {
                        let json_result = serde_json::to_string(&result).unwrap_or_else(|e| {
                            format!("[\"{}\"]", format!("JSON序列化错误: {}", e))
                        });
                        handler.send_response("success", "", json_result);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // PDF转图片
            Some("pdf_to_image") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let output_format = params["output_format"]
                    .as_str()
                    .unwrap_or("jpg")
                    .to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let is_gray = params["is_gray"].as_bool().unwrap_or(false);
                let libpath = params["libpath"]
                    .as_str()
                    .filter(|&s| !s.is_empty())
                    .map(String::from);
                let dpi = params["dpi"].as_u64().unwrap_or(300) as u32;
                let max_width_pixels = params["max_width_pixels"].as_u64().unwrap_or(3000) as u32;
                let max_height_pixels = params["max_height_pixels"].as_u64().unwrap_or(4000) as u32;
                let auto_scale_to_a4 = params["auto_scale_to_a4"].as_bool().unwrap_or(true);

                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback_with_handler(show_progress, handler.clone());

                if cfg!(any(
                    target_os = "windows",
                    target_os = "macos",
                    target_os = "linux"
                )) {
                    // 使用新的异步API
                    let handler_clone = handler.clone();
                    tokio::spawn(async move {
                        match anki::cmd::py_pdf2img_with_scaling(
                            &path,
                            &page_range,
                            &output_path,
                            dpi,
                            is_gray,
                            &output_format,
                            max_width_pixels,
                            max_height_pixels,
                            auto_scale_to_a4,
                            progress_cb,
                        )
                        .await
                        {
                            Ok(result) => {
                                let result_json =
                                    serde_json::to_string(&result).unwrap_or_default();
                                handler_clone.send_response("success", "", result_json);
                            }
                            Err(e) => {
                                handler_clone.send_response(
                                    "error",
                                    &e.to_string(),
                                    "".to_string(),
                                );
                            }
                        }
                    });
                } else {
                    // 使用pdfium库（不变）
                    let conditional_callback = create_conditional_progress_callback(show_progress);
                    match anki::pdf_utils::export_pdf_to_images_by_pdfium(
                        &path,
                        &output_path,
                        &output_format,
                        &page_range,
                        libpath,
                        is_gray,
                        conditional_callback,
                    ) {
                        Ok(result) => {
                            handler.send_response("success", "", output_path.to_string());
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                }
            }
            // EPUB转PDF
            Some("epub_to_pdf") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_epub2pdf(&path, &output_path, progress_cb).await {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // MOBI转PDF
            Some("mobi_to_pdf") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_mobi2pdf(&path, &output_path, progress_cb).await {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // OFD转PDF
            Some("ofd_to_pdf") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_ofd2pdf(&path, &output_path, progress_cb).await {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // DOCX转PDF
            Some("docx_to_pdf") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_docx2pdf(&path, &output_path, progress_cb).await {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF转DOCX
            Some("pdf_to_docx") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);
                match anki::cmd::py_pdf2docx(&path, &output_path, progress_cb).await {
                    Ok(result) => {
                        handler.send_response("success", "", result);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // 提取PDF注释
            Some("extract_annotations") => {
                let path = params["path"].as_str().unwrap_or_default().to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let num_cols = params["num_cols"].as_u64().unwrap_or(1) as u32;
                let is_full_page_mode = params["is_full_page_mode"].as_bool().unwrap_or(false);

                let handler = ResponseHandler::new(interaction_id.to_string());

                // 使用异步方式处理
                tokio::spawn(async move {
                    match anki::pdf_utils::extract_annotations(
                        &path,
                        &page_range,
                        num_cols,
                        is_full_page_mode,
                    )
                    .await
                    {
                        Ok(annotations) => {
                            let result_json =
                                serde_json::to_string(&annotations).unwrap_or_default();
                            handler.send_response("success", "", result_json);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // 文本制卡
            Some("text_card/judge") => {
                let doc_path = params["doc_path"].as_str().unwrap_or("").to_string();
                let parent_deck = params["parent_deck"].as_str().unwrap_or("").to_string();
                let address = params["address"].as_str().map(|s| s.to_string());
                let is_create_subdeck = params["is_create_subdeck"].as_bool().unwrap_or(false);
                let subdeck_prefix = params["subdeck_prefix"].as_str().unwrap_or("").to_string();
                let q_regex = params["q_regex"].as_str().unwrap_or("").to_string();
                let correct_regex = params["correct_regex"].as_str().unwrap_or("").to_string();
                let wrong_regex = params["wrong_regex"].as_str().unwrap_or("").to_string();
                let remark_regex = params["remark_regex"].as_str().unwrap_or("").to_string();
                let tags = params["tags"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();

                let result = make_judge_card(
                    &doc_path,
                    &parent_deck,
                    address.as_deref(),
                    is_create_subdeck,
                    &subdeck_prefix,
                    &q_regex,
                    &correct_regex,
                    &wrong_regex,
                    &remark_regex,
                    &tags,
                    Some(&output_path),
                    create_conditional_progress_callback(show_progress),
                )
                .await;
                let handler = ResponseHandler::new(interaction_id.to_string());
                match result {
                    Ok(result) => {
                        handler.send_response("success", "", output_path);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // PDF制卡/挖空
            Some("pdf_card/cloze") => {
                let config = OcclusionCardConfig {
                    doc_path: params["doc_path"].as_str().unwrap_or("").to_string(),
                    page_range: params["page_range"].as_str().unwrap_or("").to_string(),
                    address: params["address"].as_str().unwrap_or("").to_string(),
                    deck_name: params["deck_name"].as_str().unwrap_or("").to_string(),
                    model_name: params["model_name"].as_str().unwrap_or("").to_string(),
                    mask_types: params["mask_types"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    card_type: params["card_type"]
                        .as_str()
                        .unwrap_or("free_guess")
                        .to_string(),
                    one_card_per_cloze: params["one_card_per_cloze"].as_bool().unwrap_or(false),
                    q_mask_color: params["q_mask_color"].as_str().unwrap_or("").to_string(),
                    a_mask_color: params["a_mask_color"].as_str().unwrap_or("").to_string(),
                    tags: params["tags"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    dpi: params["dpi"].as_u64().unwrap_or(300) as u32,
                    is_full_page_mode: params["is_full_page_mode"].as_bool().unwrap_or(false),
                    is_create_subdeck: params["is_create_subdeck"].as_bool().unwrap_or(true),
                    subdeck_max_level: params["subdeck_max_level"].as_u64().unwrap_or(6) as u32,
                    is_show_source: params["is_show_source"].as_bool().unwrap_or(true),
                    is_mix_card: params["is_mix_card"].as_bool().unwrap_or(false),
                    num_cols: params["num_cols"].as_u64().unwrap_or(1) as u32,
                    extra_info: Some(params["extra_info"].as_str().unwrap_or("").to_string()),
                    item_key: params["item_key"]
                        .as_str()
                        .filter(|s| !s.is_empty())
                        .map(|s| s.to_string()),
                    output_path: params["output_path"].as_str().unwrap_or("").to_string(),
                };
                let interaction_id_clone = interaction_id.to_string();
                let result = make_occlusion_card(config, create_conditional_progress_callback(show_progress)).await;
                let handler = ResponseHandler::new(interaction_id.to_string());
                match result {
                    Ok(result) => {
                        handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // PDF制卡/问答
            Some("pdf_card/qa") => {
                let doc_path = params["doc_path"].as_str().unwrap_or("").to_string();
                let a_doc_path = params["a_doc_path"].as_str().unwrap_or("").to_string();
                let card_type = params["card_type"].as_str().unwrap_or("").to_string();
                let config = QACardConfig {
                    doc_path: doc_path,
                    a_doc_path: Some(a_doc_path),
                    page_range: params["page_range"].as_str().unwrap_or("").to_string(),
                    a_page_range: params["a_page_range"].as_str().unwrap_or("").to_string(),
                    address: params["address"].as_str().unwrap_or("").to_string(),
                    deck_name: params["deck_name"].as_str().unwrap_or("").to_string(),
                    model_name: params["model_name"].as_str().unwrap_or("").to_string(),
                    mask_types: params["mask_types"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    tags: params["tags"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    dpi: params["dpi"].as_u64().unwrap_or(300) as u32,
                    is_create_subdeck: params["is_create_subdeck"].as_bool().unwrap_or(true),
                    subdeck_max_level: params["subdeck_max_level"].as_u64().unwrap_or(0) as u32,
                    is_mix_card: params["is_mix_card"].as_bool().unwrap_or(false),
                    is_answer_cloze: params["is_answer_cloze"].as_bool().unwrap_or(false),
                    is_show_source: params["is_show_source"].as_bool().unwrap_or(true),
                    num_cols: params["num_cols"].as_u64().unwrap_or(1) as u32,
                    extra_info: Some(params["extra_info"].as_str().unwrap_or("").to_string()),
                    item_key: params["item_key"]
                        .as_str()
                        .filter(|s| !s.is_empty())
                        .map(|s| s.to_string()),
                    output_path: params["output_path"].as_str().unwrap_or("").to_string(),
                };
                let handler = ResponseHandler::new(interaction_id.to_string());
                if card_type == "single_file" {
                    let result = make_qa_card_single_file(config, create_conditional_progress_callback(show_progress)).await;
                    match result {
                        Ok(result) => {
                            handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                } else if card_type == "dual_file" {
                    let result = make_qa_card_dual_file(config, create_conditional_progress_callback(show_progress)).await;
                    match result {
                        Ok(result) => {
                            handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                } else if card_type == "single_page" || card_type == "dual_page" {
                    let result = make_qa_card_full_page(
                        params["doc_path"].as_str().unwrap_or("").to_string(),
                        params["deck_name"].as_str().unwrap_or("").to_string(),
                        params["page_range"].as_str().unwrap_or("").to_string(),
                        card_type,
                        params["output_path"].as_str().unwrap_or("").to_string(),
                        params["address"].as_str().unwrap_or("").to_string(),
                        params["tags"]
                            .as_array()
                            .unwrap_or(&Vec::new())
                            .iter()
                            .map(|s| s.as_str().unwrap_or("").to_string())
                            .collect(),
                        create_conditional_progress_callback(show_progress),
                    )
                    .await;
                    match result {
                        Ok(result) => {
                            handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                } else {
                    handler.send_response("error", "Unknown card type", "".to_string());
                }
            }
            // Zotero制卡
            Some("zotero/make_card") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let item_id = match params["item_id"].as_i64() {
                    Some(id) => id,
                    None => {
                        handler.send_response("error", "invalid item_id", "".to_string());
                        return;
                    }
                };
                let config = ZoteroCardConfig {
                    item_id,
                    page_range: params["page_range"].as_str().unwrap_or("").to_string(),
                    address: params["address"].as_str().unwrap_or("").to_string(),
                    deck_name: params["deck_name"].as_str().unwrap_or("").to_string(),
                    model_name: params["model_name"].as_str().unwrap_or("").to_string(),
                    annotation_types: params["annotation_types"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    tags: params["tags"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    is_show_source: params["is_show_source"].as_bool().unwrap_or(true),
                    is_answer_cloze: params["is_answer_cloze"].as_bool().unwrap_or(false),
                    cloze_grammar_list: params["cloze_grammar_list"]
                        .as_array()
                        .unwrap_or(&Vec::new())
                        .iter()
                        .map(|s| s.as_str().unwrap_or("").to_string())
                        .collect(),
                    extra_info: Some(params["extra_info"].as_str().unwrap_or("").to_string()),
                    output_path: params["output_path"].as_str().unwrap_or("").to_string(),
                };
                match make_zotero_annotation_cards(config, create_conditional_progress_callback(show_progress)).await {
                    Ok(result) => {
                        handler.send_response(
                            "success",
                            "",
                            params["output_path"].as_str().unwrap_or("").to_string(),
                        );
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // Zotero导出条目
            Some("zotero/export_item") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let item_id = match params["item_id"].as_i64() {
                    Some(id) => id,
                    None => {
                        handler.send_response("error", "invalid item_id", "".to_string());
                        return;
                    }
                };
                match crate::anki::pdf_card::zotero::export_zotero_item(item_id).await {
                    Ok(result) => {
                        handler.send_response("success", "", result);
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // LLM卡片生成
            Some("llm/generate_cards") => {
                let handler = ResponseHandler::new(interaction_id.to_string());

                // 提取参数
                let backend = params["backend"].as_str().unwrap_or("openai").to_string();
                let model = params["model"].as_str().unwrap_or("gpt-4o").to_string();
                let api_key = params["api_key"].as_str().unwrap_or("").to_string();
                let base_url = params["base_url"].as_str().map(|s| s.to_string());
                let system_prompt = params["system_prompt"].as_str().unwrap_or("").to_string();
                let user_prompt = params["user_prompt"].as_str().unwrap_or("").to_string();
                let temperature = params["temperature"].as_f64().unwrap_or(0.7) as f32;
                let max_tokens = params["max_tokens"].as_i64().unwrap_or(4000) as u32;
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let logdir = params["logdir"].as_str().unwrap_or("").to_string();
                let reasoning = params["reasoning"].as_bool().unwrap_or(false);
                let chunk_size = params["chunk_size"].as_i64().unwrap_or(3000) as usize;
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let cloze_mode = params["cloze_mode"].as_str().unwrap_or("").to_string();
                let is_per_cloze_per_card =
                    params["is_per_cloze_per_card"].as_bool().unwrap_or(false);
                let is_transform_formula = params["is_transform_formula"].as_bool().unwrap_or(true);
                let is_html_escape = params["is_html_escape"].as_bool().unwrap_or(false);

                // 从前端获取最大并发请求数
                let max_concurrent_requests = params["max_concurrent_requests"]
                    .as_u64()
                    .map(|v| v as usize);
                let top_p = params["top_p"].as_f64().unwrap_or(0.95) as f32;
                let top_k = params["top_k"].as_i64().unwrap_or(10) as u32;
                let timeout = params["timeout"].as_u64().unwrap_or(1200) as u32;
                let parent_deck = params["parent_deck"]
                    .as_str()
                    .unwrap_or("Guru导入")
                    .to_string();
                let tags: Vec<String> = params["tags"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect();
                let attachments = params["attachments"].as_array().map(|arr| {
                    arr.iter()
                        .filter_map(|v| v.as_str().map(String::from))
                        .collect::<Vec<String>>()
                });

                // 直接从前端接收卡片类型到模型名称的映射
                let mut card_type_models = std::collections::HashMap::new();
                if let Some(models_obj) = params["card_type_models"].as_object() {
                    for (card_type, model_value) in models_obj {
                        if let Some(model_name) = model_value.as_str() {
                            card_type_models.insert(card_type.clone(), model_name.to_string());
                        }
                    }
                }
                // 如果映射为空，使用默认映射
                if card_type_models.is_empty() {
                    // 添加默认映射
                    card_type_models.insert("qa".to_string(), "Kevin Text QA Card v2".to_string());
                }

                // 设置进度回调
                let progress_cb = create_conditional_progress_callback_with_handler(show_progress, handler.clone());

                // 发送初始进度
                progress_cb(10.0, 100.0, "正在准备LLM请求...".to_string());

                // 使用非阻塞的方式运行LLM生成任务
                let handler_clone = handler.clone();
                // 在这里直接运行异步任务 - 使用 move 关键字捕获变量
                // 为了避免线程不安全错误，传递所有引用的拷贝
                let backend = backend.to_string();
                let model = model.to_string();
                let api_key = api_key.to_string();
                let system_prompt = system_prompt.to_string();
                let user_prompt = user_prompt.to_string();
                let parent_deck = parent_deck.to_string();
                let tags_owned = tags.to_vec();
                let card_type_models_owned = card_type_models.clone();
                let output_path = output_path.to_string();
                let logdir = logdir.to_string();
                let page_range = page_range.to_string();

                tokio::spawn(async move {
                    match crate::llm::index::generate_cards(
                        &backend,
                        &model,
                        &api_key,
                        base_url,
                        &system_prompt,
                        &user_prompt,
                        temperature,
                        Some(max_tokens),
                        Some(top_p),
                        Some(top_k),
                        Some(timeout),
                        &parent_deck,
                        &tags_owned,
                        &card_type_models_owned,
                        &output_path,
                        attachments,
                        max_concurrent_requests,
                        &logdir,
                        Some(reasoning),
                        Some(chunk_size),
                        &page_range,
                        &cloze_mode,
                        is_per_cloze_per_card,
                        is_transform_formula,
                        is_html_escape,
                        progress_cb,
                    )
                    .await
                    {
                        Ok((result_path, errors)) => {
                            if errors.is_empty() {
                                handler_clone.send_response("success", "", result_path);
                            } else {
                                handler_clone.send_response(
                                    "success",
                                    &errors.join("\n"),
                                    result_path,
                                );
                            }
                        }
                        Err(err_msg) => {
                            handler_clone.send_response("error", &err_msg, "".to_string());
                        }
                    }
                });
            }
            // 导图制卡
            Some("mindmap_card") => {
                let mind_source = params["mind_source"].as_str().unwrap_or_default();
                let handler = ResponseHandler::new(interaction_id.to_string());
                match mind_source {
                    "xmind" => {
                        let config = XmindCardConfig {
                            xmind_path: params["file_path"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            address: params["address"].as_str().unwrap_or_default().to_string(),
                            deck_name: params["parent_deck"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            model_name: "Kevin Mindmap Card v3".to_string(),
                            cloze_styles: params["mask_types"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .map(|s| s.as_str().unwrap_or("").to_string())
                                .collect(),
                            text_colors: params["text_colors"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .map(|s| s.as_str().unwrap_or("").to_string())
                                .collect(),
                            use_tags: false,
                            tags: params["tags"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .map(|s| s.as_str().unwrap_or("").to_string())
                                .collect(),
                            is_show_source: params["is_show_source"].as_bool().unwrap_or(true),
                            output_path: params["output_path"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                        };

                        let result = make_xmind_card(config, create_conditional_progress_callback(show_progress)).await;
                        match result {
                            Ok(result) => {
                                handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    }
                    "zhixi" => {
                        let config = ZhixiCardConfig {
                            zxm_path: params["file_path"].as_str().unwrap_or_default().to_string(),
                            deck_name: params["parent_deck"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            model_name: "Kevin Mindmap Card v3".to_string(),
                            cloze_styles: params["mask_types"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            text_colors: params["text_colors"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            highlight_colors: params["highlight_colors"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            use_tags: params["use_tags"].as_bool().unwrap_or(false),
                            tags: params["tags"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            output_path: params["output_path"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                        };

                        let result = make_zhixi_card(config, create_conditional_progress_callback(show_progress)).await;
                        match result {
                            Ok(result) => {
                                handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    }
                    "mubu" => {
                        let config = MubuCardConfig {
                            file_path: params["file_path"].as_str().unwrap_or_default().to_string(),
                            deck_name: params["parent_deck"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            model_name: "Kevin Mindmap Card v3".to_string(),
                            cloze_styles: params["mask_types"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            text_colors: params["text_colors"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            highlight_colors: params["highlight_colors"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            tags: params["tags"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            output_path: params["output_path"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            map_id: params["map_id"].as_str().unwrap_or_default().to_string(),
                        };

                        let result = make_mubu_card(config, create_conditional_progress_callback(show_progress)).await;
                        match result {
                            Ok(result) => {
                                handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    }
                    "markdown" => {
                        let config = MarkdownCardConfig {
                            file_path: params["file_path"].as_str().unwrap_or_default().to_string(),
                            deck_name: params["parent_deck"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            model_name: "Kevin Mindmap Card v3".to_string(),
                            tags: params["tags"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                            output_path: params["output_path"]
                                .as_str()
                                .unwrap_or_default()
                                .to_string(),
                            map_id: params["map_id"].as_str().unwrap_or_default().to_string(),
                            img_paths: params["img_paths"]
                                .as_array()
                                .unwrap_or(&Vec::new())
                                .iter()
                                .filter_map(|v| v.as_str().map(String::from))
                                .collect(),
                        };

                        let result = make_markdown_card(config, create_conditional_progress_callback(show_progress)).await;
                        match result {
                            Ok(_) => {
                                handler.send_response("success", "", params["output_path"].as_str().unwrap_or("").to_string());
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    }
                    _ => {
                        handler.send_response("error", "Unknown mindmap source type", "".to_string());
                    }
                }
            }
            // TTS
            Some("tts") => {
                let lang = params["lang"].as_str().unwrap_or("zh");
                let voice = params["voice"].as_str().unwrap_or("zh-CN-XiaoxiaoNeural");
                let rate = params["rate"].as_str().unwrap_or("+0%"); // 改为Edge TTS默认格式
                let pitch = params["pitch"].as_str().unwrap_or("+0Hz"); // 改为Edge TTS默认格式
                let volume = params["volume"].as_str().unwrap_or("+0%"); // 改为Edge TTS默认格式
                let output_format = params["output_format"]
                    .as_str()
                    .unwrap_or("audio-24khz-96kbitrate-mono-mp3");
                let timeout = params["timeout"].as_u64().unwrap_or(10);
                let text = params["text"].as_str().unwrap_or("");
                let output_path = params["output_path"].as_str().unwrap_or("");
                let sub_path = params["sub_path"].as_str().unwrap_or("");
                let show_progress = params["show_progress"].as_bool().unwrap_or(true);
                // 根据平台选择不同的TTS实现
                #[cfg(any(target_os = "android", target_os = "ios"))]
                {
                    rinf::debug_print!("使用移动端TTS实现");
                    let result = gen_tts(
                        lang,
                        voice,
                        rate,
                        pitch,
                        volume,
                        output_format,
                        timeout,
                        text,
                        output_path,
                        Some(sub_path),
                    )
                    .await;
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    match result {
                        Ok(_) => {
                            handler.send_response("success", "", output_path.to_string());
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                }

                #[cfg(not(any(target_os = "android", target_os = "ios")))]
                {
                    rinf::debug_print!("使用桌面端TTS实现");
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    let progress_cb = create_conditional_progress_callback(show_progress);

                    // 克隆要在异步闭包中使用的所有变量
                    let text_clone = text.to_string();
                    let voice_clone = voice.to_string();
                    let rate_clone = rate.to_string();
                    let volume_clone = volume.to_string();
                    let pitch_clone = pitch.to_string();
                    let output_path_clone = output_path.to_string();
                    let handler_clone = handler.clone();

                    tokio::spawn(async move {
                        rinf::debug_print!("开始调用py_text_to_speech");
                        match anki::cmd::py_text_to_speech(
                            &text_clone,
                            &voice_clone,
                            &rate_clone,
                            &volume_clone,
                            &pitch_clone,
                            &output_path_clone,
                            progress_cb,
                        )
                        .await
                        {
                            Ok(result) => {
                                handler_clone.send_response("success", "", result);
                            }
                            Err(e) => {
                                handler_clone.send_response(
                                    "error",
                                    &e.to_string(),
                                    "".to_string(),
                                );
                            }
                        }
                    });
                }
            }
            // PDF旋转
            Some("pdf_rotate") => {
                let input_path = params["input_path"].as_str().unwrap_or("");
                let page_range = params["page_range"].as_str().unwrap_or("");
                let angle = params["angle"].as_i64().unwrap_or(0);
                let output_path = params["output_path"].as_str().unwrap_or("");
                let result =
                    anki::pdf_utils::rotate_pages(input_path, page_range, angle, Some(output_path));
                let handler = ResponseHandler::new(interaction_id.to_string());
                match result {
                    Ok(_) => {
                        handler.send_response("success", "", input_path.to_string());
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // PDF提取
            Some("pdf_extract") => {
                let input_path = params["doc_path"].as_str().unwrap_or("").to_string();
                let extract_type = params["extract_type"].as_str().unwrap_or("").to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();

                if extract_type == "pages" {
                    let result =
                        anki::pdf_utils::extract_pages(&input_path, &page_range, Some(&output_path));
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    match result {
                        Ok(_) => {
                            handler.send_response("success", "", input_path);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                } else if extract_type == "images" {
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    let progress_cb = create_conditional_progress_callback(show_progress);

                    // 获取可选参数
                    let image_format = params["image_format"].as_str().unwrap_or("png").to_string();
                    let min_width = params["min_width"].as_i64().unwrap_or(100) as i32;
                    let min_height = params["min_height"].as_i64().unwrap_or(100) as i32;

                    tokio::spawn(async move {
                        match anki::cmd::py_pdf_extract_images(
                            &input_path,
                            &output_path,
                            &page_range,
                            &image_format,
                            min_width,
                            min_height,
                            progress_cb,
                        )
                        .await
                        {
                            Ok(result) => {
                                handler.send_response("success", "", result);
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    });
                } else if extract_type == "pages_as_images" {
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    let progress_cb = create_conditional_progress_callback(show_progress);

                    // 获取可选参数
                    let dpi = params["dpi"].as_i64().unwrap_or(300) as i32;
                    let image_format = params["image_format"].as_str().unwrap_or("png").to_string();

                    tokio::spawn(async move {
                        match anki::cmd::py_pdf_extract_pages_as_images(
                            &input_path,
                            &output_path,
                            &page_range,
                            dpi,
                            &image_format,
                            progress_cb,
                        )
                        .await
                        {
                            Ok(result) => {
                                handler.send_response("success", "", result);
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    });
                } else if extract_type == "images" {
                    let result =
                        anki::pdf_utils::extract_images(&input_path, &page_range, &output_path);
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    match result {
                        Ok(_) => {
                            handler.send_response("success", "", input_path);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                }
            }
            // PDF缩放
            Some("pdf/scale") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let scale_mode = params["scale_mode"].as_str().unwrap_or("").to_string();
                let width = params["width"].as_f64().unwrap_or(595.0);
                let height = params["height"].as_f64().unwrap_or(842.0);
                let unit = params["unit"].as_str().unwrap_or("pt").to_string();
                let scale_ratio = params["scale_ratio"].as_f64().unwrap_or(1.0);
                let papersize = params["papersize"].as_str().unwrap_or("A4").to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_scale(
                        &input_path,
                        &output_path,
                        &scale_mode,
                        scale_ratio,
                        width,
                        height,
                        &unit,
                        &papersize,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF延展
            Some("pdf/expand") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let expand_mode = params["expand_mode"].as_str().unwrap_or("").to_string();
                let bg_path = params["bg_path"].as_str().unwrap_or("").to_string();
                let direction = params["direction"].as_str().unwrap_or("").to_string();
                let unit = params["unit"].as_str().unwrap_or("pt").to_string();
                let top = params["top"].as_f64().unwrap_or(0.0);
                let bottom = params["bottom"].as_f64().unwrap_or(0.0);
                let left = params["left"].as_f64().unwrap_or(0.0);
                let right = params["right"].as_f64().unwrap_or(0.0);
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_expand(
                        &input_path,
                        &output_path,
                        &expand_mode,
                        top,
                        bottom,
                        left,
                        right,
                        &unit,
                        &bg_path,
                        &direction,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF裁剪
            Some("pdf/crop") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let crop_type = params["crop_type"].as_str().unwrap_or("").to_string();
                let expand_mode = params["expand_mode"].as_bool().unwrap_or(false);
                let unit = params["unit"].as_str().unwrap_or("pt").to_string();
                let top = params["top"].as_f64().unwrap_or(0.0);
                let bottom = params["bottom"].as_f64().unwrap_or(0.0);
                let left = params["left"].as_f64().unwrap_or(0.0);
                let right = params["right"].as_f64().unwrap_or(0.0);
                let dpi = params["dpi"].as_u64().unwrap_or(300);
                let keep_annotation = params["keep_annotation"].as_bool().unwrap_or(true);
                let keep_page_size = params["keep_page_size"].as_bool().unwrap_or(true);
                let output_format = params["output_format"]
                    .as_str()
                    .unwrap_or("pdf")
                    .to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_crop(
                        &input_path,
                        &output_path,
                        &crop_type,
                        expand_mode,
                        top,
                        bottom,
                        left,
                        right,
                        &unit,
                        keep_annotation,
                        keep_page_size,
                        &output_format,
                        dpi,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF分割
            Some("pdf/cut") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let cut_type = params["cut_type"].as_str().unwrap_or("").to_string();
                let num_cols = params["num_cols"].as_u64().unwrap_or(0);
                let num_rows = params["num_rows"].as_u64().unwrap_or(0);
                let paper_size = params["paper_size"].as_str().unwrap_or("").to_string();
                let orientation = params["orientation"].as_str().unwrap_or("").to_string();
                let top = params["top"].as_f64().unwrap_or(10.0);
                let bottom = params["bottom"].as_f64().unwrap_or(10.0);
                let left = params["left"].as_f64().unwrap_or(10.0);
                let right = params["right"].as_f64().unwrap_or(10.0);
                // 创建拥有所有权的Vec而不是引用
                let empty_vec = Vec::new();
                let h_breakpoints_array = params["h_breakpoints"].as_array().unwrap_or(&empty_vec);
                let h_breakpoints = h_breakpoints_array.to_vec();

                let v_breakpoints_array = params["v_breakpoints"].as_array().unwrap_or(&empty_vec);
                let v_breakpoints = v_breakpoints_array.to_vec();

                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_cut(
                        &input_path,
                        &output_path,
                        &cut_type,
                        num_rows,
                        num_cols,
                        &paper_size,
                        &orientation,
                        top,
                        bottom,
                        left,
                        right,
                        &h_breakpoints,
                        &v_breakpoints,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF组合
            Some("pdf/combine") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let num_cols = params["num_cols"].as_u64().unwrap_or(0);
                let num_rows = params["num_rows"].as_u64().unwrap_or(0);
                let layout_order = params["layout_order"].as_str().unwrap_or("row_lr").to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_combine(
                        &input_path,
                        &output_path,
                        num_rows,
                        num_cols,
                        &layout_order,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF背景
            Some("pdf/background") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let background_path = params["background_path"].as_str().unwrap_or("").to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_background(
                        &input_path,
                        &background_path,
                        &output_path,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF插入
            Some("pdf/insert") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let insert_mode = params["insert_mode"].as_str().unwrap_or("").to_string();
                let insert_pos = params["insert_pos"].as_u64().unwrap_or(0);
                let pos_type = params["pos_type"].as_str().unwrap_or("").to_string();
                let count = params["count"].as_u64().unwrap_or(0);
                let orientation = params["orientation"].as_str().unwrap_or("").to_string();
                let paper_size = params["paper_size"].as_str().unwrap_or("").to_string();
                let page_range = params["page_range"].as_str().unwrap_or("").to_string();
                let doc_path2 = params["doc_path2"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_insert(
                        &input_path,
                        &output_path,
                        &insert_mode,
                        &pos_type,
                        insert_pos,
                        count,
                        &orientation,
                        &paper_size,
                        &page_range,
                        &doc_path2,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF元数据
            Some("pdf/meta") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let title = params["title"].as_str().unwrap_or("").to_string();
                let author = params["author"].as_str().unwrap_or("").to_string();
                let subject = params["subject"].as_str().unwrap_or("").to_string();
                let keywords = params["keywords"].as_str().unwrap_or("").to_string();
                let creator = params["creator"].as_str().unwrap_or("").to_string();
                let producer = params["producer"].as_str().unwrap_or("").to_string();
                let creation_date = params["creation_date"].as_str().unwrap_or("").to_string();
                let mod_date = params["mod_date"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_meta(
                        &input_path,
                        &output_path,
                        &title,
                        &author,
                        &subject,
                        &keywords,
                        &creator,
                        &producer,
                        &creation_date,
                        &mod_date,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }

            // PDF合并
            Some("pdf/merge") => {
                let input_paths: Vec<String> = params["input_paths"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|p| p.as_str().map(String::from))
                    .collect();
                let merge_mode = params["merge_mode"].as_str().unwrap_or("file").to_string();
                let input_path = params["input_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let page_range = params["page_range"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();

                #[cfg(any(target_os = "android", target_os = "ios"))]
                {
                    // 将String转换为&str的引用数组
                    let input_paths_refs: Vec<&str> =
                        input_paths.iter().map(|s| s.as_str()).collect();

                    let result = anki::pdf_utils::merge_pdfs(&input_paths_refs, &output_path);

                    let handler = ResponseHandler::new(interaction_id.to_string());
                    match result {
                        Ok(_) => {
                            handler.send_response("success", "", output_path.to_string());
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                }

                #[cfg(not(any(target_os = "android", target_os = "ios")))]
                {
                    let handler = ResponseHandler::new(interaction_id.to_string());
                    let progress_cb = create_conditional_progress_callback(show_progress);
                    let input_paths_clone = input_paths.clone();

                    tokio::spawn(async move {
                        match anki::cmd::py_merge_pdf(
                            &input_paths_clone,
                            &merge_mode,
                            &input_path,
                            &page_range,
                            &output_path,
                            progress_cb,
                        )
                        .await
                        {
                            Ok(result) => {
                                handler.send_response("success", "", result);
                            }
                            Err(e) => {
                                handler.send_response("error", &e.to_string(), "".to_string());
                            }
                        }
                    });
                }
            }
            // 获取IP
            Some("get_ip") => match anki::sync::get_all_ip_addresses() {
                Ok(ip_addresses) => {
                    RustResponse {
                        interaction_id: interaction_id.to_string(),
                        status: "success".to_string(),
                        message: "".to_string(),
                        data: serde_json::to_string(&ip_addresses).unwrap_or_default(),
                    }
                    .send_signal_to_dart();
                }
                Err(e) => {
                    RustResponse {
                        interaction_id: interaction_id.to_string(),
                        status: "error".to_string(),
                        message: e.to_string(),
                        data: "".to_string(),
                    }
                    .send_signal_to_dart();
                }
            },
            // 获取服务器状态
            Some("server_is_running") => {
                let result = sync::is_running();
                RustResponse {
                    interaction_id: interaction_id.to_string(),
                    status: "success".to_string(),
                    message: "".to_string(),
                    data: result.to_string(),
                }
                .send_signal_to_dart();
            }
            // 启动服务器 (统一使用多用户接口)
            Some("start_server") => {
                let server_path = params["server_path"].as_str().unwrap_or("").to_string();
                let host = params["host"].as_str().unwrap_or("0.0.0.0").to_string();
                let port = params["port"]
                    .as_str()
                    .unwrap_or("8080")
                    .parse::<u16>()
                    .unwrap_or(8080);
                let data_dir = params["data_dir"].as_str().unwrap_or("").to_string();

                // 解析用户数据 (支持单用户和多用户格式)
                let mut users = Vec::new();

                if let Some(users_array) = params["users"].as_array() {
                    // 多用户格式
                    for user_obj in users_array {
                        if let (Some(username), Some(password)) = (
                            user_obj["username"].as_str(),
                            user_obj["password"].as_str()
                        ) {
                            users.push(sync::SyncUser::new(
                                username.to_string(),
                                password.to_string()
                            ));
                        }
                    }
                }

                // 验证用户凭据
                if users.is_empty() {
                    RustResponse {
                        interaction_id: interaction_id.to_string(),
                        status: "error".to_string(),
                        message: "User validation failed: No users provided".to_string(),
                        data: "".to_string(),
                    }
                    .send_signal_to_dart();
                    return;
                }

                for (index, user) in users.iter().enumerate() {
                    if user.username.trim().is_empty() {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: format!("User validation failed: User {} has empty username", index + 1),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                        return;
                    }
                    if user.password.is_empty() {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: format!("User validation failed: User {} has empty password", index + 1),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                        return;
                    }
                    if user.password.len() < 6 {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: format!("User validation failed: User {} password must be at least 6 characters", index + 1),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                        return;
                    }
                }

                // Check for duplicate usernames
                let mut usernames = std::collections::HashSet::new();
                for user in &users {
                    if !usernames.insert(&user.username) {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: format!("User validation failed: Duplicate username found: {}", user.username),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                        return;
                    }
                }

                // 获取和验证payload配置
                let max_payload = {
                    // Check for explicit max_payload parameter
                    if let Some(payload_value) = params["max_payload"].as_u64() {
                        let payload_size = payload_value as usize;

                        // Validate payload size (minimum 1MB, maximum 1000MB)
                        if payload_size < 1 {
                            RustResponse {
                                interaction_id: interaction_id.to_string(),
                                status: "error".to_string(),
                                message: "Payload configuration error: Payload size must be at least 1MB".to_string(),
                                data: "".to_string(),
                            }
                            .send_signal_to_dart();
                            return;
                        }
                        if payload_size > 1000 {
                            RustResponse {
                                interaction_id: interaction_id.to_string(),
                                status: "error".to_string(),
                                message: "Payload configuration error: Payload size cannot exceed 1000MB".to_string(),
                                data: "".to_string(),
                            }
                            .send_signal_to_dart();
                            return;
                        }

                        debug_print!("Using explicit payload size: {}MB", payload_size);
                        Some(payload_size)
                    } else if let Ok(env_payload) = std::env::var("MAX_SYNC_PAYLOAD_MEGS") {
                        // Check for environment variable
                        match env_payload.parse::<usize>() {
                            Ok(payload_size) => {
                                if payload_size < 1 || payload_size > 1000 {
                                    RustResponse {
                                        interaction_id: interaction_id.to_string(),
                                        status: "error".to_string(),
                                        message: format!("Payload configuration error: Environment variable MAX_SYNC_PAYLOAD_MEGS has invalid value: {}MB (must be 1-1000)", payload_size),
                                        data: "".to_string(),
                                    }
                                    .send_signal_to_dart();
                                    return;
                                }
                                debug_print!("Using environment variable payload size: {}MB", payload_size);
                                Some(payload_size)
                            }
                            Err(_) => {
                                RustResponse {
                                    interaction_id: interaction_id.to_string(),
                                    status: "error".to_string(),
                                    message: format!("Payload configuration error: Environment variable MAX_SYNC_PAYLOAD_MEGS has invalid format: {}", env_payload),
                                    data: "".to_string(),
                                }
                                .send_signal_to_dart();
                                return;
                            }
                        }
                    } else {
                        // No explicit configuration, use default
                        debug_print!("Using default payload size configuration");
                        None
                    }
                };

                debug_print!(
                    "Starting sync server: host: {:?}, port: {:?}, data_dir: {:?}, users: {:?}, max_payload: {:?}",
                    host, port, data_dir, users.len(), max_payload
                );

                match sync::run_server_multi_user(host, port, data_dir, users, max_payload).await {
                    Ok(_) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "Sync server started successfully".to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        debug_print!("Sync server error: {}", e);
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: format!("Sync server error: {}", e),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                }
            }

            // 停止服务器
            Some("stop_server") => match anki::sync::stop_server().await {
                Ok(_) => {
                    RustResponse {
                        interaction_id: interaction_id.to_string(),
                        status: "success".to_string(),
                        message: "Server stopped".to_string(),
                        data: "".to_string(),
                    }
                    .send_signal_to_dart();
                }
                Err(e) => {
                    RustResponse {
                        interaction_id: interaction_id.to_string(),
                        status: "error".to_string(),
                        message: e.to_string(),
                        data: "".to_string(),
                    }
                    .send_signal_to_dart();
                }
            },
            // Excel验证
            Some("excel/validate") => {
                let file_path = params["file_path"].as_str().unwrap_or_default().to_string();
                match anki::excel_card::validate_excel(&file_path) {
                    Ok(result) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "".to_string(),
                            data: serde_json::to_string(&result).unwrap_or_default(),
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: e.to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                }
            }
            // Excel制卡
            Some("excel/make_card") => {
                let handler = ResponseHandler::new(interaction_id.to_string());

                // 解析必要参数
                let file_path = params["file_path"].as_str().unwrap_or_default().to_string();
                let export_card_mode = params["export_card_mode"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let sheet = params["sheet"].as_str().unwrap_or_default().to_string();
                let address = if export_card_mode == "ankiconnect" {
                    Some(params["address"].as_str().unwrap_or_default().to_string())
                } else {
                    None
                };
                let parent_deck = params["parent_deck"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let card_model = params["card_model"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let op_type = params["op_type"].as_str().unwrap_or("qa").to_string();

                // 选择题相关参数
                let answer_position = params["answer_position"]
                    .as_str()
                    .unwrap_or("answer_in_column")
                    .to_string();
                let answer_pattern = params["answer_pattern"]
                    .as_str()
                    .unwrap_or("[（(][A-G\\s]+[)）]")
                    .to_string();
                let option_position = params["option_position"]
                    .as_str()
                    .unwrap_or("option_in_column")
                    .to_string();
                let option_pattern = params["option_pattern"]
                    .as_str()
                    .unwrap_or("[（(][A-G\\s]+[)）]")
                    .to_string();

                // 解析可选参数
                let sub_deck_cols: Vec<String> = params["sub_deck_cols"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect();

                let tag_cols: Vec<String> = params["tag_cols"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect();

                let tags: Vec<String> = params["tags"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect();

                let guid_col = params["guid_col"].as_str().unwrap_or_default().to_string();

                // 解析字段映射
                let field_mappings: HashMap<String, Vec<i32>> = params["field_mappings"]
                    .as_object()
                    .unwrap_or(&serde_json::Map::new())
                    .iter()
                    .map(|(k, v)| {
                        let indices = v
                            .as_array()
                            .unwrap_or(&Vec::new())
                            .iter()
                            .filter_map(|val| val.as_str().and_then(|s| s.parse::<i32>().ok()))
                            .collect();
                        (k.clone(), indices)
                    })
                    .collect();

                // 解析 fields 参数
                let fields: Vec<String> = params["fields"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect();

                // 判断题相关参数
                let judge_answer_position = params["judge_answer_position"]
                    .as_str()
                    .unwrap_or("answer_in_column")
                    .to_string();

                let correct_pattern = params["correct_pattern"]
                    .as_str()
                    .unwrap_or("[(（]\\s*[√对是Y]\\s*[)）]")
                    .to_string();
                let wrong_pattern = params["wrong_pattern"]
                    .as_str()
                    .unwrap_or("[(（]\\s*[×错否N]\\s*[)）]")
                    .to_string();

                // 调用具体的制卡逻辑
                let result = anki::excel_card::make_excel_card(
                    &file_path,
                    &sheet,
                    address.as_deref(),
                    &parent_deck,
                    &card_model,
                    &field_mappings,
                    &fields,
                    &sub_deck_cols,
                    &tag_cols,
                    &guid_col,
                    &tags,
                    &output_path,
                    &op_type,
                    &answer_position,
                    &answer_pattern,
                    &option_position,
                    &option_pattern,
                    &judge_answer_position,
                    &correct_pattern,
                    &wrong_pattern,
                    create_conditional_progress_callback(show_progress),
                )
                .await;

                match result {
                    Ok(_) => {
                        handler.send_response("success", "", output_path.to_string());
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // HTML base64图片转本地
            Some("html/replace_base64_img") => {
                let html = params["html"].as_str().unwrap_or_default();
                let base_path = params["base_path"].as_str().unwrap_or_default();
                let html_type = params["html_type"].as_str().unwrap_or_default();
                let temp_dir = params["temp_dir"].as_str().unwrap_or_default();
                let (processed_html, image_paths) = anki::utils::replace_base64_to_local_images(
                    html, base_path, temp_dir, html_type,
                );
                let result = serde_json::json!({
                    "html": processed_html,
                    "image_paths": image_paths
                });

                RustResponse {
                    interaction_id: interaction_id.to_string(),
                    status: "success".to_string(),
                    message: "".to_string(),
                    data: serde_json::to_string(&result).unwrap_or_default(),
                }
                .send_signal_to_dart();
            }
            // HTML 本地图片转base64
            Some("html/replace_local_img") => {
                let html = params["html"].as_str().unwrap_or_default();
                let base_path = params["base_path"].as_str().unwrap_or_default();
                let html_type = params["html_type"].as_str().unwrap_or_default();
                let temp_dir = params["temp_dir"].as_str().unwrap_or_default();
                let processed_html = anki::utils::replace_local_image_paths_to_base64(
                    html, base_path, temp_dir, html_type,
                );
                let result = serde_json::json!({
                    "html": processed_html,
                });

                RustResponse {
                    interaction_id: interaction_id.to_string(),
                    status: "success".to_string(),
                    message: "".to_string(),
                    data: serde_json::to_string(&result).unwrap_or_default(),
                }
                .send_signal_to_dart();
            }
            // 新增移除根节点p标签的接口
            Some("html/remove_root_p") => {
                let html = params["html"].as_str().unwrap_or_default();
                let processed_html = anki::utils::remove_root_p_tag(html);

                RustResponse {
                    interaction_id: interaction_id.to_string(),
                    status: "success".to_string(),
                    message: "".to_string(),
                    data: processed_html,
                }
                .send_signal_to_dart();
            }
            // PDF页面重排
            Some("pdf/reorder") => {
                let input_path = params["input_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let page_range = params["page_range"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_reorder(
                        &input_path,
                        &output_path,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF拆分
            Some("pdf/split") => {
                let input_path = params["input_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let split_mode = params["split_mode"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let chunk_size = params["chunk_size"].as_u64().unwrap_or(0);
                let level = params["level"].as_u64().unwrap_or(1);
                let page_range = params["page_range"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_split(
                        &input_path,
                        &output_path,
                        &split_mode,
                        chunk_size,
                        level,
                        &page_range,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // PDF恢复权限
            Some("pdf/recover_permission") => {
                let input_path = params["input_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::cmd::py_pdf_recover_permission(
                        &input_path,
                        &output_path,
                        progress_cb,
                    )
                    .await
                    {
                        Ok(result) => {
                            handler.send_response("success", "", result);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            // 格式转换
            Some("convert/json2xlsx") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                match anki::utils::json2xlsx(&input_path, &output_path) {
                    Ok(result) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "".to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: e.to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                };
            }
            Some("convert/json2csv") => {
                let input_path = params["input_path"].as_str().unwrap_or("").to_string();
                let output_path = params["output_path"].as_str().unwrap_or("").to_string();
                let handler = ResponseHandler::new(interaction_id.to_string());
                match anki::utils::json2xlsx(&input_path, &output_path) {
                    Ok(result) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "".to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: e.to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                };
            }
            // DOCX转HTML
            Some("docx_to_html") => {
                let docx_path = params["docx_path"].as_str().unwrap_or_default().to_string();
                let output_path = params["output_path"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let escape_latex_for_js = params["escape_latex_for_js"].as_bool().unwrap_or(false);
                match docx_card::convert_docx2html(&docx_path, &output_path, escape_latex_for_js) {
                    Ok(result) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "".to_string(),
                            data: result,
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: e.to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                }
            }
            // 系统通知
            Some("send_notification") => {
                let title = params["title"].as_str().unwrap_or_default().to_string();
                let body = params["body"].as_str().unwrap_or_default().to_string();

                let handler = ResponseHandler::new(interaction_id.to_string());
                match anki::utils::send_system_notification(&title, &body) {
                    Ok(_) => {
                        handler.send_response("success", "", "".to_string());
                    }
                    Err(e) => {
                        handler.send_response("error", &e.to_string(), "".to_string());
                    }
                }
            }
            // PDF去除水印
            Some("pdf/remove_watermark") => {
                let progress_cb = create_conditional_progress_callback(show_progress);

                let input_path = params["input_path"].as_str().unwrap_or("");
                let page_range = params["page_range"].as_str().unwrap_or("");
                let output_path = params["output_path"].as_str().unwrap_or("");

                // 获取水印类型，默认为 "type" 以保持向后兼容性
                let watermark_type = params["watermark_type"].as_str().unwrap_or("type");

                // Convert to owned values to avoid borrowing issues
                let wm_indices: Vec<i32> = params["wm_index"]
                    .as_array()
                    .unwrap_or(&Vec::new())
                    .iter()
                    .filter_map(|v| v.as_str().and_then(|s| s.parse::<i32>().ok()))
                    .collect();

                // 提取其他可能的参数
                let wm_index_str = params["wm_index_str"].as_str();
                let wm_text = params["wm_text"].as_str();
                let threshold_below = params["threshold_below"].as_i64().map(|v| v as i32);
                let threshold_high = params["threshold_high"].as_i64().map(|v| v as i32);
                let threshold_target = params["threshold_target"].as_str();
                let is_limit_region = params["is_limit_region"].as_bool();
                let dpi = params["dpi"].as_i64().map(|v| v as i32);

                match anki::cmd::py_remove_watermark(
                    watermark_type,
                    input_path,
                    page_range,
                    &wm_indices,
                    wm_index_str,
                    wm_text,
                    threshold_below,
                    threshold_high,
                    threshold_target,
                    is_limit_region,
                    dpi,
                    output_path,
                    progress_cb,
                )
                .await
                {
                    Ok(_) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "".to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: e.to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                }
            }
            // PDF检测水印
            Some("pdf/detect_watermark") => {
                let progress_cb = create_conditional_progress_callback(show_progress);

                let input_path = params["input_path"].as_str().unwrap_or("");
                let page_range = params["page_range"].as_str().unwrap_or("");
                let output_path = params["output_path"].as_str().unwrap_or("");

                // 获取水印类型，默认为 "type" 以保持向后兼容性
                let watermark_type = params["watermark_type"].as_str().unwrap_or("type");

                match anki::cmd::py_detect_watermark(
                    watermark_type,
                    input_path,
                    page_range,
                    output_path,
                    progress_cb,
                )
                .await
                {
                    Ok(result) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "success".to_string(),
                            message: "".to_string(),
                            data: result,
                        }
                        .send_signal_to_dart();
                    }
                    Err(e) => {
                        RustResponse {
                            interaction_id: interaction_id.to_string(),
                            status: "error".to_string(),
                            message: e.to_string(),
                            data: "".to_string(),
                        }
                        .send_signal_to_dart();
                    }
                }
            }
            // 图片压缩
            Some("image/compress") => {
                let handler = ResponseHandler::new(interaction_id.to_string());
                let input_paths = params["input_paths"]
                    .as_array()
                    .unwrap_or(&vec![])
                    .iter()
                    .filter_map(|v| v.as_str())
                    .map(|s| s.to_string())
                    .collect::<Vec<String>>();

                if input_paths.is_empty() {
                    handler.send_response("error", "No input paths provided", "".to_string());
                    return;
                }

                let quality = params["quality"].as_u64().unwrap_or(80) as u8;
                let format = params["format"].as_str().unwrap_or("jpeg").to_string();
                let keep_metadata = params["keep_metadata"].as_bool().unwrap_or(false);
                let output_dir = params["output_dir"].as_str().map(|s| s.to_string());

                // Handle output_mode with backward compatibility for overwrite parameter
                let output_mode = if let Some(mode_str) = params["output_mode"].as_str() {
                    match mode_str.to_lowercase().as_str() {
                        "same" => anki::compress::OutputMode::Same,
                        "overwrite" => anki::compress::OutputMode::Overwrite,
                        "custom" => anki::compress::OutputMode::Custom,
                        _ => {
                            handler.send_response("error", "Invalid output_mode. Must be 'same', 'overwrite', or 'custom'", "".to_string());
                            return;
                        }
                    }
                } else {
                    // Backward compatibility: convert old overwrite boolean to new output_mode
                    let overwrite = params["overwrite"].as_bool().unwrap_or(true);
                    if overwrite {
                        anki::compress::OutputMode::Overwrite
                    } else {
                        anki::compress::OutputMode::Same
                    }
                };

                let config = anki::compress::CompressionConfig {
                    quality,
                    format,
                    keep_metadata,
                    output_dir,
                    output_mode,
                };

                // Extract progress range parameters if provided
                let progress_start = params["progress_start"].as_f64().unwrap_or(0.0);
                let progress_end = params["progress_end"].as_f64().unwrap_or(100.0);

                let progress_cb = create_conditional_progress_callback(show_progress);

                tokio::spawn(async move {
                    match anki::compress::compress_images_with_range(
                        &input_paths,
                        &config,
                        Some(progress_cb),
                        progress_start,
                        progress_end
                    ) {
                        Ok(results) => {
                            let result_json = serde_json::to_string(&results).unwrap_or_default();
                            handler.send_response("success", "", result_json);
                        }
                        Err(e) => {
                            handler.send_response("error", &e.to_string(), "".to_string());
                        }
                    }
                });
            }
            Some(op_type) => {
                debug_print!("Unknown operation type: {}", op_type);
                let handler = ResponseHandler::new(interaction_id.to_string());
                handler.send_response("error", "Unknown operation type", "".to_string());
            }
            none => {
                debug_print!("Missing operation type");
                let handler = ResponseHandler::new(interaction_id.to_string());
                handler.send_response("error", "Unknown operation type", "".to_string());
            }
        }
    }
}

pub async fn communicate_dart() {
    let receiver = DartResponse::get_dart_signal_receiver(); // GENERATED
    while let Some(dart_signal) = receiver.recv().await {
        let response = dart_signal.message;
        let interaction_id = response.interaction_id.clone();
        debug_print!("response: {:?}", &response);
        // 使用 take() 来获取所有权
        if let Some(sender) = crate::anki::utils::RESPONSE_CHANNELS
            .lock()
            .unwrap()
            .remove(&interaction_id)
        {
            let _ = sender.send(response);
        }
    }
}
// You can go with any async library, not just `tokio`.
#[tokio::main(flavor = "current_thread")]
async fn main() {
    // Spawn communicate task with automatic restart on panic
    tokio::spawn(async {
        loop {
            let task = tokio::spawn(communicate());
            let result = task.catch_unwind().await;

            match result {
                Ok(_) => {
                    // Task completed normally (unlikely for an infinite loop)
                    debug_print!("communicate task completed normally, restarting...");
                }
                Err(e) => {
                    // Task panicked
                    debug_print!("communicate task panicked: {:?}, restarting...", e);
                }
            }

            // Small delay before restarting to avoid rapid restart loops
            tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        }
    });

    // Spawn communicate_dart task with automatic restart on panic
    tokio::spawn(async {
        loop {
            let task = tokio::spawn(communicate_dart());
            let result = task.catch_unwind().await;

            match result {
                Ok(_) => {
                    // Task completed normally (unlikely for an infinite loop)
                    debug_print!("communicate_dart task completed normally, restarting...");
                }
                Err(e) => {
                    // Task panicked
                    debug_print!("communicate_dart task panicked: {:?}, restarting...", e);
                }
            }

            // Small delay before restarting to avoid rapid restart loops
            tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        }
    });

    // Keep the main function running until Dart shutdown.
    rinf::dart_shutdown().await;
}
